name: Deploy to Google Play

on:
  push:
    branches: [ main, testing ]  # Only run on main branch
  workflow_dispatch:    # Allow manual triggers

env:
  FLUTTER_VERSION: '3.29.0'
  ANDROID_SDK_ROOT: /usr/local/lib/android/sdk

jobs:
  deploy-android:
    name: Deploy to Google Play
    runs-on: ubuntu-latest
    environment: production
    strategy:
      matrix:
        flavor: [alive] #, alive

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true
          working-directory: 'android'

      - name: Cache Android SDK packages
        id: android-sdk-cache
        uses: actions/cache@v4
        with:
          path: |
            ${{ env.ANDROID_SDK_ROOT }}/cmdline-tools
            ${{ env.ANDROID_SDK_ROOT }}/platform-tools
            ${{ env.ANDROID_SDK_ROOT }}/platforms
            ${{ env.ANDROID_SDK_ROOT }}/build-tools
            ${{ env.ANDROID_SDK_ROOT }}/licenses
          key: android-sdk-${{ runner.os }}-${{ hashFiles('android-packages.txt') }}
          restore-keys: |
            android-sdk-${{ runner.os }}-        

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Get version
        run: |
          source ./version.sh
          echo "VERSION_NAME=$VERSION_NAME" >> $GITHUB_ENV
          echo "VERSION_CODE=$VERSION_CODE" >> $GITHUB_ENV

      - name: Setup environment files
        # if: ${{ matrix.flavor == 'yamiepanda' }}
        env:
          ENV_FASTLANE: ${{ secrets.ENV_FASTLANE }}
          KEYSTORE_PROPERTIES_YAMIE: ${{ secrets.KEYSTORE_PROPERTIES_YAMIE }}
          KEYSTORE_YAMIE: ${{ secrets.KEYSTORE_YAMIE }}
          FIREBASE_OPTIONS: ${{ secrets.FIREBASE_OPTIONS }}
          ENV: ${{ secrets.ENV_YAMIE }}
          GOOGLE_PLAY_SERVICE: ${{ secrets.GOOGLE_PLAY_SERVICE }}
          GOOGLE_PLAY_KEY: ${{ secrets.GOOGLE_PLAY_KEY }}
        run: |
          echo "$ENV_FASTLANE" > android/fastlane/.env
          echo "$KEYSTORE_PROPERTIES_YAMIE" > android/key.properties
          echo "$KEYSTORE_YAMIE" | base64 -d > android/app/release.key
          echo "$FIREBASE_OPTIONS" > lib/firebase_options.dart
          echo "$ENV" > .env
          echo "$GOOGLE_PLAY_SERVICE" > android/app/google-services.json
          echo "$GOOGLE_PLAY_KEY" > android/app/keys.json

      # - name: Setup environment files
      #   if: ${{ matrix.flavor != 'yamiepanda' }}
      #   env:
      #     ENV_FASTLANE: ${{ secrets.ENV_FASTLANE }}
      #     KEYSTORE_FILE: ${{ secrets.KEYSTORE_FILE }}
      #     KEYSTORE: ${{ secrets.KEYSTORE_PROPERTIES }}
      #     FIREBASE_OPTIONS: ${{ secrets.FIREBASE_OPTIONS }}
      #     GOOGLE_PLAY_SERVICE: ${{ secrets.GOOGLE_PLAY_SERVICE }}
      #     GOOGLE_PLAY_KEY: ${{ secrets.GOOGLE_PLAY_KEY }}
      #   run: |
      #     echo "$ENV_FASTLANE" > android/fastlane/.env
      #     echo "$KEYSTORE" > android/key.properties
      #     echo "$KEYSTORE_FILE" | base64 -d > android/app/release.keystore
      #     echo "$FIREBASE_OPTIONS" > lib/firebase_options.dart
      #     echo "$GOOGLE_PLAY_SERVICE" > android/app/google-services.json
      #     echo "$GOOGLE_PLAY_KEY" > android/app/keys.json

      - name: Install yq (mikefarah/yq)
        run: |
          YQ_VERSION=v4.47.1
          wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/download/${YQ_VERSION}/yq_linux_amd64
          chmod +x /usr/local/bin/yq

      - name: Create app.yml
        run: |
          printf '%s' "${{ vars.APP_CONFIG }}" > app.yml

      - name: Parse app.yml and export variables
        id: parse
        run: |
          cat app.yml
          APP_ID=$(yq e '.${{ matrix.flavor }}.app_id' app.yml)
          ASSET_URL=$(yq e '.${{ matrix.flavor }}.asset_url' app.yml)
          APP_NAME=$(yq e '.${{ matrix.flavor }}.app_name' app.yml)
          ENV=$(yq e '.${{ matrix.flavor }}.env' app.yml)

          echo "app_id=$APP_ID" >> $GITHUB_OUTPUT
          echo "asset_url=$ASSET_URL" >> $GITHUB_OUTPUT
          echo "app_name=$APP_NAME" >> $GITHUB_OUTPUT

          # also put them in job environment if you want subsequent steps to access them as env vars:
          echo "APP_ID=$APP_ID" >> $GITHUB_ENV
          echo "ASSET_URL=$ASSET_URL" >> $GITHUB_ENV
          echo "APP_NAME=$APP_NAME" >> $GITHUB_ENV

          echo $ENV | base64 -d > .env

      - name: Download and extract assets
        run: |
          echo "Downloading assets from: ${{ env.ASSET_URL }}"
          wget -O assets.zip "${{ env.ASSET_URL }}"
          unzip -o assets.zip
          ls -la
          echo "Assets extracted successfully"

      - name: Update Android configuration files
        run: |
          echo "Updating applicationId in build.gradle to: ${{ env.APP_ID }}"
          sed -i "s/applicationId .*/applicationId \"${{ env.APP_ID }}\"/g" android/app/build.gradle
          
          echo "Updating android:label in AndroidManifest.xml to: ${{ env.APP_NAME }}"
          sed -i "s/android:label=\"[^\"]*\"/android:label=\"${{ env.APP_NAME }}\"/g" android/app/src/main/AndroidManifest.xml

          echo "Updating package_name in Appfile to: ${{ env.APP_ID }}"
          sed -i "s/package_name(\"[^\"]*\")/package_name(\"${{ env.APP_ID }}\")/g" android/fastlane/Appfile
          
          # Verify changes
          echo "Verifying changes:"
          echo "build.gradle applicationId line:"
          grep "applicationId" android/app/build.gradle || echo "applicationId not found"
          echo "AndroidManifest.xml android:label line:"
          grep "android:label" android/app/src/main/AndroidManifest.xml || echo "android:label not found"
          echo "Appfile package_name line:"
          grep "package_name" android/fastlane/Appfile || echo "package_name not found"

      - name: Install dependencies & generate code
        run: |
          flutter pub get
          dart run build_runner build --delete-conflicting-outputs 

      - name: Build App Bundle
        if: ${{ matrix.flavor == 'yamiepanda' }}
        run: |
          flutter build appbundle --release --flavor ${{ matrix.flavor }} --build-number ${{ env.VERSION_CODE }} --build-name=${{ env.VERSION_NAME }}

      - name: Build App Bundle
        run: |
          flutter build appbundle --release --flavor production --build-number ${{ env.VERSION_CODE }} --build-name=${{ env.VERSION_NAME }}

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.RELEASE_TOKEN }}
          files: build/app/outputs/bundle/productionRelease/app-production-release.aab
          name: "UNIQ CRM ${{ matrix.flavor }} ${{ env.VERSION_NAME }}_${{ github.ref_name }}"
          target_commitish: "${{ github.sha }}"
          tag_name: "v${{ env.VERSION_NAME }}_${{ github.run_id }}"

      - name: Upload to Telegram
        uses: ./.github/actions/telegram-upload
        with:
          file_path: build/app/outputs/bundle/productionRelease/app-production-release.aab
          chat_id: ${{ secrets.TELEGRAM_CHAT_ID }}
          token: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          message: |
              🚀 New Mobile CRM ${{ matrix.flavor }} (App Bundle)
              📱 Version: ${{ env.VERSION_NAME }} (${{ env.VERSION_CODE }})
              🔧 Branch: ${{ github.ref_name }}

      - name: Deploy to Google Play
        working-directory: android
        run: |
          bundle install
          bundle exec fastlane publishGooglePlay variant:${{ matrix.flavor }}
