name: mobile_crm
version: 3.0.0+1741759566
publish_to: none
description: A new Flutter project.
environment:
  sdk: ^3.0.0

dependencies:
  cupertino_icons: ^1.0.5
  get: ^4.6.6
  geolocator: ^9.0.2
  json_annotation: ^4.8.1
  intl: ^0.18.1
  envied: ^0.3.0+3
  timeago: ^3.4.0
  connectivity_plus: ^6.1.0
  url_launcher: ^6.1.11
  share_plus: ^7.0.1
  rxdart: ^0.27.7

  package_info_plus: ^4.0.1

  ## UI
  lottie: ^3.1.2
  shimmer: ^3.0.0
  carousel_slider: ^5.0.0
  flutter_screenutil: ^5.9.3
  scrollable_positioned_list: ^0.3.8
  pinput: ^4.0.0
  cached_network_image: ^3.4.1

  ## Monitoring
  logger: ^1.4.0
  sentry_flutter: ^8.13.0

  ## Local Notification
  flutter_local_notifications: ^17.2.1

  ## Firebase
  firebase_auth: ^4.20.0
  firebase_core: ^2.32.0
  firebase_messaging: ^14.6.2
  firebase_analytics: ^10.4.2
  firebase_dynamic_links: ^5.3.2
  google_sign_in: ^6.1.4
  firebase_remote_config: ^4.2.2

  ## Database
  drift: ^2.8.2
  get_storage: ^2.1.1
  sqlite3_flutter_libs: ^0.5.15
  path_provider: ^2.0.15
  path: ^1.8.3

  http: ^1.0.0
  flutter:
    sdk: flutter
  device_info_plus: ^9.0.1
  flutter_svg: ^2.0.6
  flutter_rating_bar: ^4.0.1
  sign_in_with_apple: ^6.1.3
  crypto: ^3.0.3
  geocoding: ^2.1.0
  permission_handler: ^11.4.0
  image_picker: ^0.8.7+5
  open_filex: ^4.7.0
  timezone: ^0.9.2
  in_app_update: ^4.0.1
  flutter_html: ^3.0.0-beta.2
  # flutter_html: ^2.2.1
  flutter_inappwebview: ^6.1.5
  infinite_scroll_pagination: ^4.0.0
  in_app_review: ^2.0.6
  palette_generator: ^0.3.3+2
  fluttertoast: ^8.2.2
  flutter_staggered_grid_view: ^0.7.0
  google_maps_flutter: ^2.5.3
  flutter_slidable: ^3.1.0
  # sentry: ^7.20.1
  # speech_to_text: ^7.0.0
  # speech_to_text:
  #   path: ../speech_to_text/speech_to_text
  speech_to_text:
    git:
      url: https://github.com/uniqdev-id/speech_to_text.git
      ref: fix/web # branch name
      path: speech_to_text
  avatar_glow: ^2.0.2
  collection: ^1.19.0
  coupon_uikit: ^0.2.1

dependency_overrides:
  firebase_core: ^3.4.1
  web: ^0.5.1

dev_dependencies:
  flutter_lints: ^2.0.1
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter
  drift_dev: ^2.8.3
  build_runner: ^2.4.6
  build_web_compilers: ^4.0.9
  json_serializable: ^6.7.0
  envied_generator: ^0.3.0+3

flutter:
  assets:
    - assets/
    - assets/icons/
    - assets/png/
    - assets/icons/svg/
    - assets/lottie/
    - assets/icon_app/
    - shorebird.yaml
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins/Poppins-Medium.ttf
        - asset: assets/fonts/Poppins/Poppins-SemiBold.ttf
        - asset: assets/fonts/Poppins/Poppins-Bold.ttf
        - asset: assets/fonts/Poppins/Poppins-ExtraBold.ttf
        - asset: assets/fonts/Poppins/Poppins-Black.ttf
  uses-material-design: true
