#!/bin/bash

#installing fvm
curl -fsSL https://fvm.app/install.sh | bash

# Array of Flutter versions to test
FLUTTER_VERSIONS=("3.22.3" "3.24.0" "3.24.1" "3.24.2" "3.24.3")

# Create a log file with timestamp
LOG_FILE="flutter_version_test_$(date +%Y%m%d_%H%M%S).log"

# Function to log messages
log_message() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" | tee -a "$LOG_FILE"
}

# Function to check if a command was successful
check_status() {
    if [ $? -eq 0 ]; then
        log_message "✅ Success: $1"
        return 0
    else
        log_message "❌ Failed: $1"
        return 1
    fi
}

# Function to build the app
build_app() {
    local version=$1
    
    log_message "🔄 Starting build process for Flutter $version"
    
    # Switch to the specified Flutter version
    log_message "Switching to Flutter version $version"
    fvm install $version
    fvm use $version
    check_status "Switch to Flutter $version" || return 1
    
    # Run flutter clean
    log_message "Running flutter clean"
    fvm flutter clean
    check_status "Flutter clean" || return 1
    
    # Run flutter pub get
    log_message "Running flutter pub get"
    fvm flutter pub get
    check_status "Flutter pub get" || return 1
    
    # Run build runner
    log_message "Running build runner"
    fvm dart run build_runner build --delete-conflicting-outputs
    check_status "Build runner" || return 1
    
    # Build debug APK
    log_message "Building debug APK"
    fvm flutter build apk --debug --flavor dev
    check_status "Build debug APK" || return 1
    
    log_message "✨ Successfully completed all steps for Flutter $version"
    return 0
}

# Main execution
log_message "🚀 Starting Flutter version compatibility test"
log_message "Versions to test: ${FLUTTER_VERSIONS[*]}"

for version in "${FLUTTER_VERSIONS[@]}"; do
    log_message "================================================"
    log_message "Testing Flutter version $version"
    
    if build_app "$version"; then
        log_message "✅ Version $version passed all tests"
    else
        log_message "❌ Version $version failed. Stopping tests."
        exit 1
    fi
done

log_message "🎉 All specified Flutter versions were tested successfully!"