import 'dart:async';
import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:mobile_crm/app/enum/permission_type_enum.dart';
import 'package:mobile_crm/app/exception/in_app_update_exception_handler.dart';
import 'package:mobile_crm/app/helper/sentry_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/network_connectivity.dart';
import 'package:mobile_crm/app/utils/permission_handler_helper.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/app_modal_bottom_sheet.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/config_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/notification_me_repository.dart';
import 'package:mobile_crm/data/services/notification_service.dart';
import 'package:mobile_crm/domain/repository/config_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../data/models/fcm_model.dart';
import '../../../../data/repository/product_repository.dart';
import '../../../../data/services/local_storage.dart';
import '../../../../domain/repository/product_repository.dart';

class WrapperController extends FullLifeCycleController
    with FullLifeCycleMixin {
  final ConfigRepository _repo;
  final _repoNotification = Get.find<NotificationMeRepositoryIml>();
  final ProductRepository repoProduct = Get.find<ProductRepositoryIml>();
  final store = LocalStorageService();
  final NetworkConnectivity _networkConnectivity = NetworkConnectivity.instance;
  WrapperController(this._repo);

  Map _source = {ConnectivityResult.none: false};

  var configApp = ConfigData().obs;
  var faqs = <FaqData>[].obs;
  var internetConnectionIsConnect = '';
  var platform = const EventChannel('firebaseChannel');

  DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  double iosVersion = 0.0;

  @override
  void onInit() {
    // _networkCon();
    _getConfig();
    _getFaq();
    _repo.getConfigLocal();
    if (GetPlatform.isIOS) {
      getDeviceVersion();
    }
    checkNotificationDeal();
    if (GetPlatform.isAndroid) {
      checkForUpdate();
    }
    getCurrentStreamNotification();
    Future.delayed(
      const Duration(milliseconds: 1500),
      () {
        listenForDataFromPlatform();
      },
    );
    PermissionHandlerHelper.requestMultiplePermission(
        [PermissionTypeEnum.notification, PermissionTypeEnum.location]);
    super.onInit();
  }

  Future<String> getSdkVersion() async {
    var deviceInfo = await deviceInfoPlugin.androidInfo;
    return deviceInfo.version.sdkInt.toString();
  }

// Listen for data from the channel
  Future<void> listenForDataFromPlatform() async {
    await FirebaseMessaging.instance.getInitialMessage().then((value) async {
      if (value != null) {
        FcmModel fcmModel = FcmModel.fromJson(value.data);
        switch (fcmModel.type) {
          case 'deal':
            Get.toNamed(Routes.DEALDETAIL("${fcmModel.id}"));
            break;
          case 'voucher':
            Get.toNamed(Routes.VOUCHERDETAIL("${fcmModel.id}"));
            break;
          case 'url':
            await launchUrl(Uri.parse(fcmModel.id ?? ''));
            break;
          case 'inbox':
            if (fcmModel.id != null) {
              Get.toNamed(Routes.NOTIFICATIONDETAIL("${fcmModel.id}"));
            } else {
              Get.toNamed(Routes.NOTIFICATION);
            }
            break;
          case 'sales':
            Get.toNamed(Routes.NOTIFICATIONSALES("${fcmModel.id}"));
            break;
          case 'product':
            Future.delayed(
              const Duration(seconds: 1),
              () async {
                Product? product =
                    await repoProduct.getProductByDetailId(fcmModel.id ?? '0');
                if (product == null) {
                  Toast.show(
                      Strings.valueNotFound.trParams({'value': "Product"}),
                      type: ToastType.dark);
                  return;
                }
                if (Get.context != null) {
                  ShowCustomModalBottom.showModalProductNotif(
                      Get.context!, product, this);
                }
              },
            );
            break;
          default:
            Get.toNamed(Routes.NOTIFICATION);
        }
      }
    });
  }

  void getCurrentStreamNotification() {
    // notificationService.
    NotificationService.notificationActionStream.listen((event) async {
      if (event?.payload != null) {
        FcmModel fcmModel = FcmModel.fromJson(jsonDecode(event?.payload ?? ''));
        if (fcmModel.type != null) {
          switch (fcmModel.type) {
            case 'deal':
              Get.toNamed(Routes.DEALDETAIL("${fcmModel.id}"));
              break;
            case 'voucher':
              Get.toNamed(Routes.VOUCHERDETAIL("${fcmModel.id}"));
              break;
            case 'url':
              await launchUrl(Uri.parse(fcmModel.id ?? ''));
              break;
            case 'inbox':
              if (fcmModel.id != null) {
                Get.toNamed(Routes.NOTIFICATIONDETAIL("${fcmModel.id}"));
              } else {
                Get.toNamed(Routes.NOTIFICATION);
              }
              break;
            case 'sales':
              Get.toNamed(Routes.NOTIFICATIONSALES("${fcmModel.id}"));
              break;
            case 'product':
              Product? product =
                  await repoProduct.getProductByDetailId(fcmModel.id ?? '0');
              if (product == null) {
                Toast.show(Strings.valueNotFound.trParams({'value': "Product"}),
                    type: ToastType.dark);
                return;
              }
              if (Get.context != null) {
                ShowCustomModalBottom.showModalProductNotif(
                    Get.context!, product, this);
              }
              break;
            default:
              Get.toNamed(Routes.NOTIFICATION);
          }
        }
      }
    });
  }

  @override
  void onClose() {
    _networkConnectivity.disposeStream();
    super.onClose();
  }

  Asset? getAsset() => configApp.value.asset;

  ColorConfig? getColor() => getAsset()?.color;

  String getPointLanguage() =>
      configApp.value.language?.getPointLanguage ?? 'Point';

  Color getPrimaryColor() => getColor()?.getPrimaryColor ?? AppColor.black90;

  Color getPrimaryDarkColor() =>
      getColor()?.getPrimaryDarkColor ?? AppColor.black90;

  Color getSecondaryColor() =>
      getColor()?.getSecondaryColor ?? AppColor.black90;

  void _networkCon() async {
    _networkConnectivity.initialise();
    _networkConnectivity.myStream.listen((source) {
      _source = source;
      if (Get.context != null) {
        if (_source.values.toList().first == false) {
          Get.showSnackbar(
            GetSnackBar(
              messageText: Center(
                  child: Text(
                Strings.noInternetConnection.tr,
                style:
                    AppFont.componentSmallBold.copyWith(color: AppColor.white),
              )),
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
              duration: const Duration(hours: 24),
            ),
          );
          internetConnectionIsConnect = "false";
        }
        if (internetConnectionIsConnect == "false" &&
            _source.values.toList().first == true) {
          Get.closeCurrentSnackbar();
          Get.closeAllSnackbars();
          Get.showSnackbar(
            GetSnackBar(
              messageText: Center(
                  child: Text(Strings.internetConnectionRestored.tr,
                      style: AppFont.componentSmallBold
                          .copyWith(color: AppColor.white))),
              // message: 'Internet Connection Restored',
              backgroundColor: Colors.green,
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
              duration: const Duration(seconds: 3),
            ),
          );
          internetConnectionIsConnect = "true";
        }
      }

      if (configApp.value == ConfigModel()) {
        _getConfig();
      }
    });
  }

  Future<void> checkForUpdate() async {
    try {
      if (GetPlatform.isAndroid) {
        await InAppUpdate.checkForUpdate().then((updateInfo) {
          if (updateInfo.updateAvailability ==
              UpdateAvailability.updateAvailable) {
            if (updateInfo.immediateUpdateAllowed) {
              InAppUpdate.performImmediateUpdate().then((appUpdateResult) {
                if (appUpdateResult == AppUpdateResult.success) {
                  return;
                }
              });
            } else if (updateInfo.flexibleUpdateAllowed) {
              InAppUpdate.startFlexibleUpdate().then((appUpdateResult) {
                if (appUpdateResult == AppUpdateResult.success) {
                  InAppUpdate.completeFlexibleUpdate();
                }
              });
            }
          }
        });
      }
    } on PlatformException catch (e) {
      var result = InAppUpdateExceptionHandler.handleException(e);
      if (result != '') {
        Toast.show(result, type: ToastType.error, duration: 5);
      }
    } catch (e, s) {
      errorLogger(pos: "In App Update", error: e, stackTrace: s);
      await SentryHelper.logException(e, s);
    }
  }

  void _getConfig() async {
    try {
      configApp.value = await _repo.getConfigLocal();
    } catch (e, s) {
      errorLogger(pos: 'Wrapper Controller', error: e, stackTrace: s);
    }
  }

  void _getFaq() async {
    try {
      faqs.value = await _repo.getFaq();
    } catch (e, s) {
      errorLogger(pos: "Wrapper Controller", error: e, stackTrace: s);
    }
  }

  Future getDeviceVersion() async {
    var iosInfo = await deviceInfoPlugin.iosInfo;
    iosVersion = double.tryParse(iosInfo.systemVersion) ?? 0;
  }

  Future<void> checkNotificationDeal() async {
    List<NotificationMeData>? listNotification =
        await _repoNotification.getAll();
    listNotification = listNotification.where((element) {
      var now = DateTime.now();
      var nowDeal =
          DateTime.fromMillisecondsSinceEpoch(element.scheduleTime ?? 0);

      if (nowDeal.difference(now).inHours <= 24 &&
          !nowDeal.difference(now).isNegative) {
        return true;
      }
      return false;
    }).toList();
    if (listNotification.isNotEmpty) {
      for (var element in listNotification) {
        NotificationService.pushNotificationWithSchedule(element);
      }
    }
  }

  @override
  void onDetached() {}

  @override
  void onInactive() {}

  @override
  void onPaused() {}

  @override
  void onResumed() {}

  @override
  void onHidden() {
    // TODO: implement onHidden
  }
}
