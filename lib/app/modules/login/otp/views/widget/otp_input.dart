import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:pinput/pinput.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class OtpInput extends StatefulWidget {
  const OtpInput({Key? key}) : super(key: key);

  @override
  State<OtpInput> createState() => _OtpInputState();
}

class _OtpInputState extends State<OtpInput> {
  final focusNode = FocusNode();

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OtpController>();
    final wrapperController = Get.find<WrapperController>();
    final defaultPinTheme = PinTheme(
      width: AppDimen.h40,
      height: AppDimen.h40,
      textStyle: AppFont.componentSmallBold.copyWith(
          color: wrapperController.getPrimaryColor(), fontSize: 14.sp),
      decoration: BoxDecoration(
        color: AppColor.whiteGrey,
        border: Border.all(
            color: wrapperController.getPrimaryColor(),
            width: 1,
            style: BorderStyle.solid),
        borderRadius: BorderRadius.circular(AppDimen.h8),
      ),
    );

    return Pinput(
      key: const Key('pinInputOTPKey'),
      length: 6,
      controller: controller.smsOtpController,
      focusNode: focusNode,
      autofocus: false,
      closeKeyboardWhenCompleted: true,
      androidSmsAutofillMethod: AndroidSmsAutofillMethod.smsRetrieverApi,
      defaultPinTheme: defaultPinTheme,
      animationCurve: Curves.fastOutSlowIn,
      animationDuration: const Duration(milliseconds: 300),
      keyboardType: TextInputType.number,
      // separator: AppDimen.h8.width,
      separatorBuilder: (index) => AppDimen.h8.width ,
      onChanged: (value) => controller.onChange += 1,
      onCompleted: (value) async {
        infoLogger("value on completed", "value $value");
        if (!controller.isLoading.value) {
          Sentry.addBreadcrumb(Breadcrumb(
              type: 'debug',
              category: 'user.activity.otp.pinput.oncomplete',
              data: {"pin_input": controller.smsOtpController.text},
              level: SentryLevel.debug));
          if (controller.smsOtpController.text.length < 6) {
            return await Toast.show('Invalid OTP', type: ToastType.error);
          }
          controller.verifyCodeOTP(controller.smsOtpController.text.toString());
        }
      },
      focusedPinTheme: defaultPinTheme.copyWith(
        textStyle: AppFont.componentSmallBold.copyWith(color: AppColor.white),
        decoration: BoxDecoration(
          color: AppColor.black,
          borderRadius: BorderRadius.circular(AppDimen.h8),
          border: Border.all(
              color: AppColor.white, width: 3, style: BorderStyle.solid),
        ),
      ),
      showCursor: false,
    );
  }
}
