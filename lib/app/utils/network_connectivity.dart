import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;

class NetworkConnectivity {
  NetworkConnectivity._();

  static final _instance = NetworkConnectivity._();

  static NetworkConnectivity get instance => _instance;
  final _networkConnectivity = Connectivity();
  final _controller = StreamController.broadcast();

  Stream get myStream => _controller.stream;

  // 1.
  void initialise() async {
    List<ConnectivityResult> result = await _networkConnectivity.checkConnectivity();    
    _checkStatus(result[0]);
    _networkConnectivity.onConnectivityChanged.listen((result) {
      _checkStatus(result[0]);
    });
  }

// 2.
  void _checkStatus(ConnectivityResult result) async {
    bool isOnline = false;
    try {
      final result = await http.get(Uri.parse('https://www.google.com'));
      isOnline = result.statusCode == 200;
    } on SocketException catch (_) {
      isOnline = false;
    }
    _controller.sink.add({result: isOnline});
  }

  void disposeStream() => _controller.close();
}
