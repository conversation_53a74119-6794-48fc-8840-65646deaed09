plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services" version "4.4.0" apply false
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

// def flutterRoot = localProperties.getProperty('flutter.sdk')
// if (flutterRoot == null) {
//     throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
// }

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

// apply plugin: 'com.android.application'
// START: FlutterFire Configuration
// apply plugin: 'com.google.gms.google-services'
// END: FlutterFire Configuration
// apply plugin: 'kotlin-android'
// apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"


android {
    compileSdkVersion 35
    ndkVersion "26.3.11579264"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "id.uniq.crm"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled 
        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
        }
    }

    flavorDimensions "version"

    productFlavors {
        production {
            dimension "version"
        }
        dev {
            dimension "version"
            versionNameSuffix "-dev"
        }

        yamiepanda {
            dimension "version"
            applicationId "com.uniq.uniqmembership.yamiepanda"
        }

        alive {
            dimension "version"
            applicationId "com.uniq.uniqmembership.alive"
        }

        prod {
            dimension "version"
            versionNameSuffix "-prod"
        }
    }

}

flutter {
    source '../..'
}

dependencies {
    def activity_version = "1.7.2"

    implementation "androidx.activity:activity-ktx:$activity_version"
    // implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'
    implementation 'androidx.window:window:1.0.0'
    implementation 'androidx.window:window-java:1.0.0'
    implementation("androidx.browser:browser:1.4.0")
    implementation platform('com.google.firebase:firebase-bom:31.0.3')
    implementation 'com.google.android.play:app-update-ktx:2.0.1'
    implementation 'com.android.support:multidex:1.0.3'
}
